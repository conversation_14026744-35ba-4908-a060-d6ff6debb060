# API Integration for Video Processing

This document describes the API integration for video processing with subtitles.

## Overview

The application now supports two video processing methods:

1. **API Processing** (default): Sends video and subtitle data to a backend server
2. **Local Processing**: Uses FFmpeg.wasm for client-side processing

## API Endpoints

### Submit Video Processing Task

**POST** `http://localhost:8888/api/capability/executeAction`

**Request Format:**
- Content-Type: `multipart/form-data`
- Fields:
  - `videoFile`: The video file to process
  - `srtFile`: Bilingual SRT subtitle file (merged original and translated)
  - `srtOptions`: JSON string with processing options

**SRT Options Format:**
```json
{
  "embedOriginalSubtitles": true,
  "embedTranslatedSubtitles": true,
  "subtitleStyle": {
    "fontFamily": "Arial",
    "fontSize": 18,
    "fontColor": "white",
    "backgroundColor": "rgba(0,0,0,0.8)",
    "position": "bottom",
    "outline": true,
    "outlineColor": "black"
  },
  "outputFormat": "mp4"
}
```

**Response:**
```json
{
  "taskId": "1748280774_v4uvx_QpKx",
  "status": "pending",
  "message": "Task submitted successfully"
}
```

### Check Task Status

**GET** `http://localhost:8888/api/capability/subtitleTask?taskId={taskId}`

**Response:**
```json
{
  "taskId": "1748280774_v4uvx_QpKx",
  "status": "processing",
  "progress": 45,
  "message": "Processing video with subtitles...",
  "error": null,
  "downloadUrl": null,
  "filename": null,
  "size": null
}
```

**Status Values:**
- `pending`: Task is queued
- `processing`: Task is being processed
- `completed`: Task completed successfully
- `failed`: Task failed with error

**Completed Response:**
```json
{
  "taskId": "1748280774_v4uvx_QpKx",
  "status": "completed",
  "progress": 100,
  "message": "Video processing completed",
  "error": null,
  "downloadUrl": "http://localhost:8888/api/download/1748280774_v4uvx_QpKx.mp4",
  "filename": "processed_video.mp4",
  "size": 15728640
}
```

## Bilingual SRT File Format

The client creates a single bilingual SRT file by merging original and translated subtitles:

**Example bilingual SRT content:**
```
1
00:00:01,000 --> 00:00:03,000
Hello, welcome to our tutorial
Hola, bienvenido a nuestro tutorial

2
00:00:04,000 --> 00:00:06,000
Today we will learn about cooking
Hoy aprenderemos sobre cocinar

3
00:00:07,000 --> 00:00:09,000
Let's start with the basics
Comencemos con lo básico
```

**Format Rules:**
- Each subtitle entry contains both original and translated text
- Original text appears on the first line
- Translated text appears on the second line (separated by newline)
- If only original or translated is enabled, only that text appears
- Standard SRT timing format is maintained

## Implementation Details

### Client-Side Flow

1. User clicks "Generate Video with Subtitles (API)"
2. Client merges original and translated subtitles into bilingual SRT format
3. Client creates SRT file blob and prepares FormData with video file, SRT file, and options
4. Client submits POST request to `/executeAction`
5. Server responds with task ID
6. Client polls `/subtitleTask?taskId={id}` every 2 seconds
7. When status is "completed", client downloads the processed video
8. Client displays progress updates throughout the process

### Error Handling

- Network errors: Falls back to local processing if configured
- Server errors: Displays error message with suggestion to check server
- Timeout: Stops polling after 10 minutes (300 attempts × 2 seconds)

### Configuration

Users can toggle between API and local processing in the UI:
- **API Processing**: Faster, requires backend server
- **Local Processing**: Slower, works offline, no server required

## Testing the API

To test the API integration:

1. Start your backend server on `localhost:8888`
2. Ensure the endpoints are implemented according to the specification
3. Upload a video file in the application
4. Generate subtitles
5. Enable "Use API Processing" checkbox
6. Click "Generate Video with Subtitles (API)"
7. Monitor the progress and check for successful completion

## Backend Server Requirements

Your backend server should:

1. Accept multipart/form-data uploads
2. Process video files with subtitle embedding
3. Return task IDs for tracking
4. Provide progress updates via polling endpoint
5. Serve processed video files for download
6. Handle errors gracefully and return appropriate status codes

## Example Backend Response Flow

1. **Submit Task**: Return task ID immediately
2. **Processing**: Update progress from 0-100%
3. **Completion**: Provide download URL for processed video
4. **Error**: Return error status with descriptive message

The client will automatically handle the polling and progress updates based on your server's responses.
