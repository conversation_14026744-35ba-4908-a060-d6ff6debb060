# AI Subtitle Studio

An application to automatically generate transcriptions from video descriptions, create subtitles, edit them, and embed them directly into videos using FFmpeg. Uses Gemini API for transcription generation and translation.

## Features

- **AI-Powered Subtitle Generation**: Generate original and translated subtitles using Google's Gemini AI
- **Real-time Video Processing**: Embed subtitles directly into videos using FFmpeg.wasm
- **Subtitle Editing**: Full-featured subtitle editor with drag-and-drop reordering
- **Multiple Export Formats**: Export subtitles as SRT files or embed them into MP4, WebM, or AVI videos
- **Customizable Styling**: Adjust font size, color, position, and outline for embedded subtitles
- **Live Preview**: Preview subtitles on your video before processing
- **Bilingual Support**: Generate and display both original and translated subtitles

## Video Processing

The application now includes real FFmpeg-powered video processing capabilities:

- **Subtitle Embedding**: Burn subtitles directly into video files
- **Multiple Formats**: Support for MP4, WebM, and AVI output formats
- **Styling Options**: Customize font size, color, position, and text outline
- **Progress Tracking**: Real-time progress updates during video processing
- **Dual Language Support**: Embed both original and translated subtitles simultaneously

## Setup

**Prerequisites:** Node.js

1. Install dependencies: `pnpm install` (or `npm install`)
2. Set the `GEMINI_API_KEY` in [.env.local](.env.local) to your Gemini API key
3. Run the development server: `pnpm dev` (or `npm run dev`)

## Usage

1. Upload a video file or provide a description
2. Configure source and target languages
3. Generate subtitles using AI
4. Edit subtitles as needed
5. Customize subtitle styling
6. Process video to embed subtitles
7. Download the processed video

## Technical Details

- Built with React, TypeScript, and Vite
- Uses FFmpeg.wasm for client-side video processing
- Integrates with Google Gemini AI for subtitle generation
- Responsive design with Tailwind CSS
