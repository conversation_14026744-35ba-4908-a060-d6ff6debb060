import { MergedSubtitle, VideoProcessingOptions, VideoProcessingProgress, ProcessedVideoResult, SubtitleStyle } from '../types';
import { generateSRTContent } from '../utils/subtitleUtils';

export interface VideoProcessingRequest {
  videoFile: File;
  srtContent: string;
  srtOptions: {
    embedOriginalSubtitles: boolean;
    embedTranslatedSubtitles: boolean;
    subtitleStyle: SubtitleStyle;
    outputFormat: 'mp4' | 'webm' | 'avi';
  };
}

/**
 * {
    "error": 0,
    "msg": "Task created successfully",
    "data": {
        "task_id": "1748419566_sWhOGwrs"
    },
    "code": 0
}
 */
export interface TaskResponse {
  error: number;
  msg: string;
  data: {
    task_id: string;
  };
  code: number;
}

/**
 * {
    "error": 0,
    "msg": "Success",
    "data": {
        "task_id": "1748419566_sWhOGwrs",
        "status": "processing",
        "process_percent": 50,
        "message": "Embedding subtitles into video"
    },
    "code": 0
}
 */

export interface TaskStatusResponse {
  error: number;
  msg: string;
  data: {
    task_id: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    process_percent: number;
    message: string;
    video_url?: string;
    filename?: string;
    size?: number;
  };
  code: number;
}

class ApiVideoService {
  private baseUrl = 'http://localhost:8888';
  private progressCallback: ((progress: VideoProcessingProgress) => void) | null = null;
  private pollingInterval: number = 2000; // 2 seconds
  private maxPollingAttempts: number = 300; // 10 minutes max (300 * 2 seconds)
  private lastDownloadUrl: string | null = null;

  setProgressCallback(callback: (progress: VideoProcessingProgress) => void) {
    this.progressCallback = callback;
  }

  private updateProgress(stage: VideoProcessingProgress['stage'], progress: number, message: string, error?: string) {
    if (this.progressCallback) {
      this.progressCallback({ stage, progress, message, error });
    }
  }

  async processVideoWithSubtitles(
    videoFile: File,
    subtitles: MergedSubtitle[],
    options: VideoProcessingOptions
  ): Promise<ProcessedVideoResult> {
    try {
      this.updateProgress('initializing', 5, 'Preparing video processing request...');

      // Generate bilingual SRT content by merging original and translated subtitles
      const bilingualSrtLines = subtitles
        .filter(sub => {
          const hasOriginal = options.embedOriginalSubtitles && sub.originalText.trim();
          const hasTranslated = options.embedTranslatedSubtitles && sub.translatedText.trim();
          return hasOriginal || hasTranslated;
        })
        .map(sub => {
          let combinedText = '';

          // Add original text if enabled and available
          if (options.embedOriginalSubtitles && sub.originalText.trim()) {
            combinedText += sub.originalText.trim();
          }

          // Add translated text if enabled and available
          if (options.embedTranslatedSubtitles && sub.translatedText.trim()) {
            if (combinedText) {
              combinedText += '\n'; // New line between original and translated
            }
            combinedText += sub.translatedText.trim();
          }

          return {
            id: sub.id,
            startTime: sub.startTime,
            endTime: sub.endTime,
            text: combinedText,
          };
        });

      if (bilingualSrtLines.length === 0) {
        throw new Error('No subtitle content to embed');
      }

      // Generate the bilingual SRT content
      const srtContent = generateSRTContent(bilingualSrtLines);

      this.updateProgress('processing_video', 10, 'Uploading video and subtitle data...');

      // Create SRT file blob
      const srtBlob = new Blob([srtContent], { type: 'text/srt;charset=utf-8' });
      const srtFileName = `subtitles_${Date.now()}.srt`;

      // Prepare form data
      const formData = new FormData();
      formData.append('videoFile', videoFile);
      formData.append('srtFile', srtBlob, srtFileName); // Send as file instead of text
      formData.append('srtOptions', JSON.stringify({
        embedOriginalSubtitles: options.embedOriginalSubtitles,
        embedTranslatedSubtitles: options.embedTranslatedSubtitles,
        subtitleStyle: options.subtitleStyle,
        outputFormat: options.outputFormat,
        ttsService: options.ttsService,
        ttsLanguageTarget: options.ttsLanguageTarget,
        sourceLanguage: options.sourceLanguage,
        targetLanguage: options.targetLanguage,
      }));

      // Submit processing request
      console.log('Submitting video processing request to:', `${this.baseUrl}/executeAction`);
      const response = await fetch(`${this.baseUrl}/api/capability/task`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API request failed:', response.status, errorText);
        throw new Error(`Failed to submit video processing request: ${response.status} ${errorText}`);
      }

      const taskResponse: TaskResponse = await response.json();
      this.updateProgress('processing_video', 15, `Task submitted with ID: ${taskResponse.data.task_id}`);

      // Poll for task completion
      const result = await this.pollTaskStatus(taskResponse.data.task_id);
      return result;

    } catch (error) {
      console.error('API video processing error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred during video processing';
      this.updateProgress('error', 0, 'Video processing failed', errorMessage);
      throw new Error(`Video processing failed: ${errorMessage}`);
    }
  }

  private async pollTaskStatus(taskId: string): Promise<ProcessedVideoResult> {
    let attempts = 0;

    while (attempts < this.maxPollingAttempts) {
      try {
        this.updateProgress('processing_video', 20 + (attempts / this.maxPollingAttempts) * 70,
          `Checking task status... (${attempts + 1}/${this.maxPollingAttempts})`);

        const response = await fetch(`${this.baseUrl}/api/capability/task/${taskId}`);

        if (!response.ok) {
          throw new Error(`Failed to check task status: ${response.status}`);
        }

        const status: TaskStatusResponse = await response.json();

        // Update progress based on task status
        this.updateProgress('processing_video', Math.max(20, status.data.process_percent), status.data.message);

        switch (status.data.status) {
          case 'completed':
            if (!status.data.video_url) {
              throw new Error('Task completed but no download URL provided');
            }
            return await this.downloadProcessedVideo(status.data);

          case 'failed':
            throw new Error(status.data.message || 'Video processing failed on server');

          case 'processing':
          case 'pending':
            // Continue polling
            break;

          default:
            throw new Error(`Unknown task status: ${status.data.status}`);
        }

        attempts++;

        if (attempts < this.maxPollingAttempts) {
          await new Promise(resolve => setTimeout(resolve, this.pollingInterval));
        }

      } catch (error) {
        console.error('Error polling task status:', error);
        if (attempts >= this.maxPollingAttempts - 1) {
          throw error;
        }
        attempts++;
        await new Promise(resolve => setTimeout(resolve, this.pollingInterval));
      }
    }

    throw new Error('Video processing timed out. Please try again.');
  }

  private async downloadProcessedVideo(statusData: TaskStatusResponse['data']): Promise<ProcessedVideoResult> {
    try {
      this.updateProgress('finalizing', 95, 'Preparing download...');

      // Store the download URL for the UI to access
      this.lastDownloadUrl = statusData.video_url || null;

      this.updateProgress('completed', 100, 'Video processing completed - ready for download');

      // Return a result with the download URL for the UI to show a download button
      return {
        videoBlob: new Blob(), // Empty blob since we're providing download URL instead
        filename: statusData.filename || 'processed_video.mp4',
        duration: 0, // Duration not provided by API
        size: statusData.size || 0
      };

    } catch (error) {
      console.error('Error preparing download:', error);
      throw new Error(`Failed to prepare download: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Method to get the last download URL
  getLastDownloadUrl(): string | null {
    return this.lastDownloadUrl;
  }

  // Method to clear the download URL
  clearDownloadUrl(): void {
    this.lastDownloadUrl = null;
  }

  // Method to trigger manual download by opening URL in new tab
  triggerManualDownload(videoUrl?: string, filename?: string): void {
    try {
      const urlToUse = videoUrl || this.lastDownloadUrl;

      if (!urlToUse) {
        throw new Error('No download URL available');
      }

      // Create a temporary anchor element to trigger download
      const link = document.createElement('a');
      link.href = urlToUse;
      link.download = filename || 'processed_video.mp4';
      link.target = '_blank';
      link.rel = 'noopener noreferrer';

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('Manual download triggered for:', urlToUse);
    } catch (error) {
      console.error('Failed to trigger manual download:', error);
      // Fallback: just open the URL in a new tab if we have one
      const urlToUse = videoUrl || this.lastDownloadUrl;
      if (urlToUse) {
        window.open(urlToUse, '_blank', 'noopener,noreferrer');
      }
    }
  }

  // Method to cancel a task (if the API supports it)
  async cancelTask(taskId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/cancelTask`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ taskId }),
      });

      if (!response.ok) {
        console.warn(`Failed to cancel task ${taskId}: ${response.status}`);
      }
    } catch (error) {
      console.warn('Error canceling task:', error);
    }
  }

  // Method to get task history (if the API supports it)
  async getTaskHistory(): Promise<TaskStatusResponse[]> {
    try {
      const response = await fetch(`${this.baseUrl}/taskHistory`);

      if (!response.ok) {
        throw new Error(`Failed to get task history: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting task history:', error);
      return [];
    }
  }
}

export const apiVideoService = new ApiVideoService();
