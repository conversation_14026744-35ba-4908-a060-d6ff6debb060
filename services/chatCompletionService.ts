import { MergedSubtitle } from '../types';
import { formatSecondsToSRTTime } from '../utils/subtitleUtils';

export interface ChatCompletionRequest {
  provider: 'openai' | 'gemini';
  prompt: string;
  stream: boolean;
}

export interface ChatCompletionResponse {
  error: number;
  msg: string;
  data: {
    response: string;
    provider: string;
    model: string;
    usage: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
  };
  code: number;
}

const extractLanguageName = (langString: string): string => {
  if (!langString || typeof langString !== 'string') return "the specified language";
  const match = langString.match(/^([^(]+)/);
  return match ? match[1].trim() : langString;
}

export const generateSubtitlesWithChatCompletion = async (
  provider: 'openai' | 'gemini',
  videoDescription?: string,
  videoFile?: File | null,
  originalAudioLanguageFull?: string, // e.g., "English (en)"
  targetLanguageFull?: string        // e.g., "Spanish (es)"
): Promise<MergedSubtitle[]> => {
  const originalLanguageName = extractLanguageName(originalAudioLanguageFull || "the original language");
  const targetLanguageName = extractLanguageName(targetLanguageFull || "the target language");

  let promptSourceDescription = videoDescription || "a short video";
  // if (videoFile) {
  //   promptSourceDescription = `a video named "${videoFile.name}"`;
  //   if (videoDescription && videoDescription.trim() !== "") {
  //     promptSourceDescription += ` with the following theme or content: "${videoDescription.trim()}"`;
  //   } else {
  //     promptSourceDescription += ` (no specific theme provided, infer from general video content if possible)`;
  //   }
  // } else if (videoDescription && videoDescription.trim() !== "") {
  //   promptSourceDescription = `a short video with the following theme or content: "${videoDescription.trim()}"`;
  // } else {
  //   throw new Error("Cannot generate transcription: No video file uploaded and no video description provided.");
  // }

  const prompt = `
    You are an expert multilingual transcription and translation service.
    Your task is to transcribe audio from ${promptSourceDescription} and then immediately translate each line.
    The original audio is in ${originalLanguageName}.
    Translate each transcribed line into ${targetLanguageName}.

    Provide the output as a sequence of subtitle entries.
    For EACH subtitle entry, you MUST follow this exact multi-line structure:
    <index_number>
    <start_timestamp> --> <end_timestamp>  (timestamp format: HH:MM:SS,mmm)
    <text_in_${originalLanguageName}>
    <text_in_${targetLanguageName}>

    (Ensure there is ONE blank line separating each complete subtitle entry from the next index_number. Do not add extra blank lines within an entry.)

    Generate approximately 10-15 subtitle entries.
    Ensure timestamps are sequential and represent realistic speech segment durations (e.g., 2-7 seconds per line, with short gaps between them).
    The first subtitle should start after 00:00:00,500.

    Example of a single entry:
    1
    00:00:01,234 --> 00:00:04,567
    This is the original sentence in ${originalLanguageName}.
    This is the translated sentence in ${targetLanguageName}.

    Do not include any other explanations, introductory text, or any text outside of this specified SRT-like bilingual format.
    The entire response should be only the structured subtitle entries.
  `;

  try {
    const requestBody: ChatCompletionRequest = {
      provider,
      prompt,
      stream: false
    };

    const response = await fetch('http://localhost:8888/api/chat/completion', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Chat completion API request failed:', response.status, errorText);
      throw new Error(`Failed to generate subtitles: ${response.status} ${errorText}`);
    }

    const chatResponse: ChatCompletionResponse = await response.json();
    
    if (chatResponse.error !== 0) {
      throw new Error(`API Error: ${chatResponse.msg}`);
    }

    const rawOutput = chatResponse.data.response;
    const subtitles: MergedSubtitle[] = [];
    const blocks = rawOutput.split(/\n\s*\n/); // Split by one or more newlines, effectively by blank lines

    let cumulativeTime = 0.5; // Start first subtitle around 0.5 seconds

    for (const block of blocks) {
      const lines = block.trim().split('\n');
      if (lines.length < 4) { // Expecting index, timestamps, original, translated
        continue;
      }

      const timeLine = lines[1].trim();
      const originalText = lines[2].trim();
      const translatedText = lines[3].trim();

      const timeMatch = timeLine.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
      if (!timeMatch) {
        // Fallback: generate timestamps if parsing fails
        const wordCount = originalText.split(/\s+/).length;
        const estimatedDuration = Math.max(2, Math.min(wordCount * 0.5, 7));
        const startTime = formatSecondsToSRTTime(cumulativeTime);
        cumulativeTime += estimatedDuration;
        const endTime = formatSecondsToSRTTime(cumulativeTime);
        cumulativeTime += 0.2; // Small gap

        subtitles.push({
          id: crypto.randomUUID(),
          startTime,
          endTime,
          originalText,
          translatedText,
        });
        continue;
      }

      const [, startTime, endTime] = timeMatch;

      subtitles.push({
        id: crypto.randomUUID(),
        startTime,
        endTime,
        originalText,
        translatedText,
      });
    }
    
    if (subtitles.length === 0 && rawOutput.trim() !== "") {
      // Fallback parsing
      console.warn("Bilingual SRT parsing failed, attempting simple line parsing as a fallback.");
      const plainLines = rawOutput.split('\n').filter(line => line.trim() !== '');
      if (plainLines.length > 0) {
        const singleSubtitle: MergedSubtitle = {
          id: crypto.randomUUID(),
          startTime: formatSecondsToSRTTime(0.5),
          endTime: formatSecondsToSRTTime(3.5),
          originalText: plainLines.join(' '),
          translatedText: "(Translation fallback: Please translate manually or re-generate)",
        };
        return [singleSubtitle];
      }
      throw new Error("Failed to parse transcription response. Output was not empty but no valid subtitles found.");
    }

    return subtitles;

  } catch (error) {
    console.error("Error generating subtitles with chat completion:", error);
    let errorMessage = "Failed to generate subtitles.";
    if (error instanceof Error) {
      errorMessage += ` Details: ${error.message}`;
    }
    throw new Error(errorMessage);
  }
};
