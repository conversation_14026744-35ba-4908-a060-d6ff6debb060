
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { MergedSubtitle } from '../types'; // Changed SubtitleLine to MergedSubtitle
import { formatSecondsToSRTTime } from '../utils/subtitleUtils';

const API_KEY = process.env.API_KEY;
let ai: GoogleGenAI | null = null;

if (API_KEY) {
  ai = new GoogleGenAI({ apiKey: API_KEY });
} else {
  console.error("API_KEY environment variable not found. Gemini API calls will fail or use mocked data if implemented.");
}

const modelName = 'gemini-2.5-flash-preview-04-17';

const checkAiInitialized = () => {
  if (!ai) {
    throw new Error("Gemini API key is not configured or client failed to initialize. Please set the API_KEY environment variable.");
  }
  return ai;
}

const extractLanguageName = (langString: string): string => {
  if (!langString || typeof langString !== 'string') return "the specified language";
  const match = langString.match(/^([^(]+)/);
  return match ? match[1].trim() : langString;
}

export const generateMockTranscriptionWithTimestamps = async (
  videoDescription?: string,
  videoFile?: File | null,
  originalAudioLanguageFull?: string, // e.g., "English (en)"
  targetLanguageFull?: string        // e.g., "Spanish (es)"
): Promise<MergedSubtitle[]> => {
  const currentAi = checkAiInitialized();
  
  const originalLanguageName = extractLanguageName(originalAudioLanguageFull || "the original language");
  const targetLanguageName = extractLanguageName(targetLanguageFull || "the target language");

  let promptSourceDescription = "";
  if (videoFile) {
    promptSourceDescription = `a video named "${videoFile.name}"`;
    if (videoDescription && videoDescription.trim() !== "") {
      promptSourceDescription += ` with the following theme or content: "${videoDescription.trim()}"`;
    } else {
      promptSourceDescription += ` (no specific theme provided, infer from general video content if possible)`;
    }
  } else if (videoDescription && videoDescription.trim() !== "") {
    promptSourceDescription = `a short video with the following theme or content: "${videoDescription.trim()}"`;
  } else {
    throw new Error("Cannot generate transcription: No video file uploaded and no video description provided.");
  }

  const prompt = `
    You are an expert multilingual transcription and translation service.
    Your task is to transcribe audio from ${promptSourceDescription} and then immediately translate each line.
    The original audio is in ${originalLanguageName}.
    Translate each transcribed line into ${targetLanguageName}.

    Provide the output as a sequence of subtitle entries.
    For EACH subtitle entry, you MUST follow this exact multi-line structure:
    <index_number>
    <start_timestamp> --> <end_timestamp>  (timestamp format: HH:MM:SS,mmm)
    <text_in_${originalLanguageName}>
    <text_in_${targetLanguageName}>

    (Ensure there is ONE blank line separating each complete subtitle entry from the next index_number. Do not add extra blank lines within an entry.)

    Generate approximately 10-15 subtitle entries.
    Ensure timestamps are sequential and represent realistic speech segment durations (e.g., 2-7 seconds per line, with short gaps between them).
    The first subtitle should start after 00:00:00,500.

    Example of a single entry:
    1
    00:00:01,234 --> 00:00:04,567
    This is the original sentence in ${originalLanguageName}.
    This is the translated sentence in ${targetLanguageName}.

    Do not include any other explanations, introductory text, or any text outside of this specified SRT-like bilingual format.
    The entire response should be only the structured subtitle entries.
  `;

  try {
    const response: GenerateContentResponse = await currentAi.models.generateContent({
      model: modelName,
      contents: prompt,
      config: {
        temperature: 0.5, // Adjust temperature for creative but plausible transcription/translation
      }
    });
    
    const rawOutput = response.text;
    const subtitles: MergedSubtitle[] = [];
    const blocks = rawOutput.split(/\n\s*\n/); // Split by one or more newlines, effectively by blank lines

    let cumulativeTime = 0.5; // Start first subtitle around 0.5 seconds

    for (const block of blocks) {
      const lines = block.trim().split('\n');
      if (lines.length < 4) { // Expecting index, timestamps, original, translated
        // console.warn("Skipping malformed block:", block);
        continue;
      }

      // const index = parseInt(lines[0].trim(), 10); // We don't strictly need the index from response
      const timeLine = lines[1].trim();
      const originalText = lines[2].trim();
      const translatedText = lines[3].trim();

      const timeMatch = timeLine.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
      if (!timeMatch) {
        // console.warn("Skipping block with invalid time format:", block);
        // Fallback: generate timestamps if Gemini fails
        const wordCount = originalText.split(/\s+/).length;
        const estimatedDuration = Math.max(2, Math.min(wordCount * 0.5, 7));
        const startTime = formatSecondsToSRTTime(cumulativeTime);
        cumulativeTime += estimatedDuration;
        const endTime = formatSecondsToSRTTime(cumulativeTime);
        cumulativeTime += 0.2; // Small gap

         subtitles.push({
          id: crypto.randomUUID(),
          startTime,
          endTime,
          originalText,
          translatedText,
        });
        continue;
      }

      const [, startTime, endTime] = timeMatch;

      subtitles.push({
        id: crypto.randomUUID(),
        startTime,
        endTime,
        originalText,
        translatedText,
      });
    }
    
    if (subtitles.length === 0 && rawOutput.trim() !== "") {
        // This might happen if Gemini provides text but not in the expected format.
        // As a last resort, try to parse it as simple lines if the strict format fails.
        // This part is a fallback and might need refinement based on observed Gemini behavior.
        console.warn("Bilingual SRT parsing failed, attempting simple line parsing as a fallback.");
        const plainLines = rawOutput.split('\n').filter(line => line.trim() !== '');
        if (plainLines.length > 0) {
            const singleSubtitle: MergedSubtitle = {
                id: crypto.randomUUID(),
                startTime: formatSecondsToSRTTime(0.5),
                endTime: formatSecondsToSRTTime(3.5),
                originalText: plainLines.join(' '),
                translatedText: "(Translation fallback: Please translate manually or re-generate)",
            };
            return [singleSubtitle];
        }
        throw new Error("Failed to parse transcription response. Output was not empty but no valid subtitles found.");
    }


    return subtitles;

  } catch (error) {
    console.error("Error generating bilingual transcription:", error);
    let errorMessage = "Failed to generate transcription.";
    if (error instanceof Error) {
      errorMessage += ` Details: ${error.message}`;
    }
     if (error && typeof error === 'object' && 'message' in error && (error as any).message.includes('API key not valid')) {
        errorMessage = "Invalid API Key. Please ensure your API_KEY environment variable is correctly set.";
    }
    throw new Error(errorMessage);
  }
};

// Helper function to translate a single piece of text (can remain for individual translations)
async function translateText(text: string, targetLanguageFull: string): Promise<string> {
  const currentAi = checkAiInitialized();
  const targetLanguageName = extractLanguageName(targetLanguageFull);
  const prompt = `Translate the following text to ${targetLanguageName}. Provide only the translation, without any introductory phrases like "Here is the translation:" or explanations. Maintain the original meaning and tone as much as possible. If the text is very short or a name, translate it if appropriate, otherwise return it as is. Text to translate: "${text}"`;

  try {
    const response: GenerateContentResponse = await currentAi.models.generateContent({
      model: modelName,
      contents: prompt,
       config: {
        temperature: 0.3, 
      }
    });
    return response.text.trim();
  } catch (error) {
    console.error(`Error translating text: "${text}" to ${targetLanguageName}:`, error);
    return `(Translation Error for: ${text})`; // Return original with error indication
  }
}

// This function will translate existing original texts in MergedSubtitle objects
export const translateExistingSubtitles = async (subtitles: MergedSubtitle[], targetLanguageFull: string): Promise<MergedSubtitle[]> => {
  checkAiInitialized();
  const translatedSubtitles: MergedSubtitle[] = [];

  for (const subtitle of subtitles) {
    if (!subtitle.originalText.trim()) { 
        translatedSubtitles.push({
            ...subtitle,
            translatedText: "" // Keep translated text empty if original is empty
        });
        continue;
    }
    const translatedText = await translateText(subtitle.originalText, targetLanguageFull);
    translatedSubtitles.push({
      ...subtitle, 
      translatedText: translatedText,
    });
  }
  return translatedSubtitles;
};
