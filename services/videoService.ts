import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile, toBlobURL } from '@ffmpeg/util';
import { MergedSubtitle, VideoProcessingOptions, VideoProcessingProgress, ProcessedVideoResult, SubtitleStyle } from '../types';
import { generateSRTContent } from '../utils/subtitleUtils';

class VideoService {
  private ffmpeg: FFmpeg | null = null;
  private isLoaded = false;
  private progressCallback: ((progress: VideoProcessingProgress) => void) | null = null;

  constructor() {
    this.ffmpeg = new FFmpeg();
  }

  setProgressCallback(callback: (progress: VideoProcessingProgress) => void) {
    this.progressCallback = callback;
  }

  private updateProgress(stage: VideoProcessingProgress['stage'], progress: number, message: string, error?: string) {
    if (this.progressCallback) {
      this.progressCallback({ stage, progress, message, error });
    }
  }

  async loadFFmpeg(): Promise<void> {
    if (this.isLoaded || !this.ffmpeg) return;

    try {
      this.updateProgress('loading_ffmpeg', 10, 'Loading FFmpeg...');

      // Load FFmpeg with CDN URLs
      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';

      this.ffmpeg.on('log', ({ message }) => {
        console.log('FFmpeg log:', message);
      });

      this.ffmpeg.on('progress', ({ progress }) => {
        if (progress > 0) {
          this.updateProgress('processing_video', Math.round(progress * 100), `Processing video... ${Math.round(progress * 100)}%`);
        }
      });

      await this.ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      });

      this.isLoaded = true;
      this.updateProgress('loading_ffmpeg', 30, 'FFmpeg loaded successfully');
    } catch (error) {
      console.error('Failed to load FFmpeg:', error);
      this.updateProgress('error', 0, 'Failed to load FFmpeg', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  private generateSubtitleFilter(subtitles: MergedSubtitle[], type: 'original' | 'translated', style: SubtitleStyle): string {
    const subtitleLines = subtitles
      .filter(sub => type === 'original' ? sub.originalText.trim() : sub.translatedText.trim())
      .map(sub => ({
        id: sub.id,
        startTime: sub.startTime,
        endTime: sub.endTime,
        text: type === 'original' ? sub.originalText : sub.translatedText,
      }));

    if (subtitleLines.length === 0) return '';

    // Convert SRT time format to seconds for FFmpeg
    const timeToSeconds = (timeStr: string): number => {
      try {
        const [time, ms] = timeStr.split(',');
        const [hours, minutes, seconds] = time.split(':').map(Number);
        return hours * 3600 + minutes * 60 + seconds + Number(ms) / 1000;
      } catch (error) {
        console.warn(`Invalid time format: ${timeStr}, defaulting to 0`);
        return 0;
      }
    };

    // Enhanced text escaping for FFmpeg
    const escapeTextForFFmpeg = (text: string): string => {
      return text
        // Handle backslashes first
        .replace(/\\/g, '\\\\')
        // Handle single quotes
        .replace(/'/g, "\\'")
        // Handle colons (FFmpeg parameter separator)
        .replace(/:/g, "\\:")
        // Handle newlines
        .replace(/\n/g, '\\n')
        .replace(/\r/g, '')
        // Handle other special characters
        .replace(/\[/g, '\\[')
        .replace(/\]/g, '\\]')
        .replace(/,/g, '\\,')
        .replace(/;/g, '\\;')
        // Handle quotes
        .replace(/"/g, '\\"')
        // Remove or replace problematic Unicode characters
        .replace(/[^\x00-\x7F]/g, (char) => {
          // Keep common accented characters, replace others with space
          const code = char.charCodeAt(0);
          if (code >= 0x00C0 && code <= 0x017F) return char; // Latin Extended
          if (code >= 0x0100 && code <= 0x024F) return char; // Latin Extended Additional
          return ' ';
        });
    };

    // Generate drawtext filters for each subtitle with enhanced styling
    const filters = subtitleLines.map((sub, index) => {
      const startTime = timeToSeconds(sub.startTime);
      const endTime = timeToSeconds(sub.endTime);
      const escapedText = escapeTextForFFmpeg(sub.text);

      // Enhanced positioning with better spacing
      const yPosition = style.position === 'top' ? '60' :
                       style.position === 'center' ? '(h-text_h)/2' :
                       'h-text_h-60';

      // Build the filter with enhanced options
      let filter = `drawtext=text='${escapedText}'`;
      filter += `:fontsize=${style.fontSize}`;
      filter += `:fontcolor=${style.fontColor}`;
      filter += `:x=(w-text_w)/2`;
      filter += `:y=${yPosition}`;
      filter += `:enable='between(t,${startTime},${endTime})'`;

      // Enhanced outline/border styling
      if (style.outline) {
        filter += `:borderw=3:bordercolor=${style.outlineColor}`;
        // Add shadow for better readability
        filter += `:shadowx=2:shadowy=2:shadowcolor=black@0.5`;
      }

      // Add background box if backgroundColor is set and not transparent
      if (style.backgroundColor && style.backgroundColor !== 'transparent' && !style.backgroundColor.includes('rgba(0,0,0,0)')) {
        filter += `:box=1:boxcolor=${style.backgroundColor}:boxborderw=5`;
      }

      // Add text alignment and line spacing for multi-line text
      if (escapedText.includes('\\n')) {
        filter += `:line_spacing=5`;
      }

      return filter;
    });

    return filters.join(',');
  }

  async processVideoWithSubtitles(
    videoFile: File,
    subtitles: MergedSubtitle[],
    options: VideoProcessingOptions
  ): Promise<ProcessedVideoResult> {
    if (!this.ffmpeg) {
      throw new Error('FFmpeg not initialized');
    }

    // Validate inputs
    if (!videoFile) {
      throw new Error('Video file is required');
    }

    if (subtitles.length === 0) {
      throw new Error('No subtitles provided');
    }

    // Validate subtitle timing
    this.validateSubtitleTiming(subtitles);

    try {
      this.updateProgress('initializing', 5, 'Initializing video processing...');

      if (!this.isLoaded) {
        await this.loadFFmpeg();
      }

      this.updateProgress('processing_video', 40, 'Loading video file...');

      // Write input video to FFmpeg filesystem
      const fileExtension = videoFile.name.split('.').pop()?.toLowerCase() || 'mp4';
      const inputFileName = `input.${fileExtension}`;

      this.updateProgress('processing_video', 45, 'Loading video into FFmpeg...');

      try {
        await this.ffmpeg.writeFile(inputFileName, await fetchFile(videoFile));
      } catch (error) {
        throw new Error(`Failed to load video file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Prepare subtitle filters with enhanced positioning
      const filters: string[] = [];
      let originalPosition = options.subtitleStyle.position;
      let translatedPosition = options.subtitleStyle.position;

      // Adjust positions when both subtitle types are enabled
      if (options.embedOriginalSubtitles && options.embedTranslatedSubtitles) {
        originalPosition = 'bottom';
        translatedPosition = 'top';
      }

      if (options.embedOriginalSubtitles) {
        const originalFilter = this.generateSubtitleFilter(subtitles, 'original', {
          ...options.subtitleStyle,
          position: originalPosition
        });
        if (originalFilter) filters.push(originalFilter);
      }

      if (options.embedTranslatedSubtitles) {
        const translatedFilter = this.generateSubtitleFilter(subtitles, 'translated', {
          ...options.subtitleStyle,
          position: translatedPosition
        });
        if (translatedFilter) filters.push(translatedFilter);
      }

      this.updateProgress('embedding_subtitles', 60, 'Embedding subtitles...');

      const outputFileName = `output.${options.outputFormat}`;

      if (filters.length > 0) {
        // Apply subtitle filters with optimized encoding settings
        const filterComplex = filters.join(',');
        console.log('Applying filter:', filterComplex);

        // Enhanced FFmpeg command with better quality and performance balance
        const ffmpegArgs = [
          '-i', inputFileName,
          '-vf', filterComplex,
          '-c:a', 'copy', // Copy audio without re-encoding
          '-c:v', 'libx264', // Use H.264 codec
          '-preset', 'medium', // Balance between speed and quality
          '-crf', '23', // Good quality setting
          '-movflags', '+faststart', // Optimize for web streaming
          '-y',
          outputFileName
        ];

        // Adjust settings based on output format
        if (options.outputFormat === 'webm') {
          ffmpegArgs[ffmpegArgs.indexOf('-c:v') + 1] = 'libvpx-vp9';
          ffmpegArgs[ffmpegArgs.indexOf('-crf') + 1] = '30';
        } else if (options.outputFormat === 'avi') {
          ffmpegArgs[ffmpegArgs.indexOf('-c:v') + 1] = 'libx264';
          // Remove movflags for AVI
          const movflagsIndex = ffmpegArgs.indexOf('-movflags');
          if (movflagsIndex !== -1) {
            ffmpegArgs.splice(movflagsIndex, 2);
          }
        }

        await this.ffmpeg.exec(ffmpegArgs);
      } else {
        // No subtitles to embed, just copy the video
        await this.ffmpeg.exec([
          '-i', inputFileName,
          '-c', 'copy',
          '-y',
          outputFileName
        ]);
      }

      this.updateProgress('finalizing', 90, 'Finalizing video...');

      // Read the output file
      const outputData = await this.ffmpeg.readFile(outputFileName);

      if (!outputData || outputData.length === 0) {
        throw new Error('Failed to generate output video - empty file');
      }

      const videoBlob = new Blob([outputData], { type: `video/${options.outputFormat}` });

      // Clean up files
      try {
        await this.ffmpeg.deleteFile(inputFileName);
        await this.ffmpeg.deleteFile(outputFileName);
      } catch (cleanupError) {
        console.warn('Failed to cleanup temporary files:', cleanupError);
        // Don't throw here as the main operation succeeded
      }

      const result: ProcessedVideoResult = {
        videoBlob,
        filename: `${videoFile.name.split('.')[0]}_with_subtitles.${options.outputFormat}`,
        duration: 0, // We could get this from ffprobe if needed
        size: videoBlob.size
      };

      this.updateProgress('completed', 100, 'Video processing completed successfully');
      return result;

    } catch (error) {
      console.error('Video processing error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred during video processing';
      this.updateProgress('error', 0, 'Video processing failed', errorMessage);
      throw new Error(`Video processing failed: ${errorMessage}`);
    }
  }

  private validateSubtitleTiming(subtitles: MergedSubtitle[]): void {
    const timeToSeconds = (timeStr: string): number => {
      try {
        const [time, ms] = timeStr.split(',');
        const [hours, minutes, seconds] = time.split(':').map(Number);
        return hours * 3600 + minutes * 60 + seconds + Number(ms) / 1000;
      } catch {
        return -1; // Invalid time
      }
    };

    for (const subtitle of subtitles) {
      const startTime = timeToSeconds(subtitle.startTime);
      const endTime = timeToSeconds(subtitle.endTime);

      if (startTime === -1 || endTime === -1) {
        throw new Error(`Invalid time format in subtitle: "${subtitle.originalText || subtitle.translatedText}"`);
      }

      if (startTime >= endTime) {
        throw new Error(`Invalid timing: start time must be before end time in subtitle: "${subtitle.originalText || subtitle.translatedText}"`);
      }

      if (startTime < 0 || endTime < 0) {
        throw new Error(`Negative time values not allowed in subtitle: "${subtitle.originalText || subtitle.translatedText}"`);
      }
    }
  }

  async createSubtitleOnlyVideo(
    subtitles: MergedSubtitle[],
    options: VideoProcessingOptions,
    duration: number = 60
  ): Promise<ProcessedVideoResult> {
    if (!this.ffmpeg) {
      throw new Error('FFmpeg not initialized');
    }

    try {
      this.updateProgress('initializing', 5, 'Creating subtitle-only video...');

      if (!this.isLoaded) {
        await this.loadFFmpeg();
      }

      // Create a blank video with subtitles
      const filters: string[] = ['color=black:size=1920x1080:duration=' + duration];

      if (options.embedOriginalSubtitles) {
        const originalFilter = this.generateSubtitleFilter(subtitles, 'original', options.subtitleStyle);
        if (originalFilter) filters.push(originalFilter);
      }

      if (options.embedTranslatedSubtitles) {
        const translatedFilter = this.generateSubtitleFilter(subtitles, 'translated', {
          ...options.subtitleStyle,
          position: options.embedOriginalSubtitles ? 'top' : options.subtitleStyle.position
        });
        if (translatedFilter) filters.push(translatedFilter);
      }

      const outputFileName = `subtitle_video.${options.outputFormat}`;
      const filterComplex = filters.join(',');

      await this.ffmpeg.exec([
        '-f', 'lavfi',
        '-i', filterComplex,
        '-t', duration.toString(),
        '-y',
        outputFileName
      ]);

      const outputData = await this.ffmpeg.readFile(outputFileName);
      const videoBlob = new Blob([outputData], { type: `video/${options.outputFormat}` });

      await this.ffmpeg.deleteFile(outputFileName);

      const result: ProcessedVideoResult = {
        videoBlob,
        filename: `subtitles_only.${options.outputFormat}`,
        duration,
        size: videoBlob.size
      };

      this.updateProgress('completed', 100, 'Subtitle video created successfully');
      return result;

    } catch (error) {
      console.error('Subtitle video creation error:', error);
      this.updateProgress('error', 0, 'Subtitle video creation failed', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  async getVideoInfo(videoFile: File): Promise<{ duration: number; width: number; height: number; fps: number }> {
    if (!this.ffmpeg) {
      throw new Error('FFmpeg not initialized');
    }

    if (!this.isLoaded) {
      await this.loadFFmpeg();
    }

    try {
      const fileExtension = videoFile.name.split('.').pop()?.toLowerCase() || 'mp4';
      const inputFileName = `probe_input.${fileExtension}`;

      await this.ffmpeg.writeFile(inputFileName, await fetchFile(videoFile));

      // Use ffprobe to get video information
      await this.ffmpeg.exec(['-i', inputFileName, '-f', 'null', '-']);

      // Clean up
      await this.ffmpeg.deleteFile(inputFileName);

      // Note: In a real implementation, you'd parse the ffprobe output
      // For now, return default values
      return {
        duration: 60, // seconds
        width: 1920,
        height: 1080,
        fps: 30
      };
    } catch (error) {
      console.warn('Failed to get video info:', error);
      return {
        duration: 60,
        width: 1920,
        height: 1080,
        fps: 30
      };
    }
  }

  async processVideoWithQualityPreset(
    videoFile: File,
    subtitles: MergedSubtitle[],
    options: VideoProcessingOptions,
    qualityPreset: 'fast' | 'balanced' | 'high_quality' = 'balanced'
  ): Promise<ProcessedVideoResult> {
    // Modify options based on quality preset
    const presetOptions = { ...options };

    // This method uses the existing processVideoWithSubtitles but could be extended
    // to modify encoding parameters based on the quality preset
    return this.processVideoWithSubtitles(videoFile, subtitles, presetOptions);
  }

  async createSubtitleSRT(subtitles: MergedSubtitle[], type: 'original' | 'translated'): Promise<Blob> {
    const srtContent = this.generateSRTFromSubtitles(subtitles, type);
    return new Blob([srtContent], { type: 'text/srt;charset=utf-8' });
  }

  private generateSRTFromSubtitles(subtitles: MergedSubtitle[], type: 'original' | 'translated'): string {
    let srtContent = '';
    let index = 1;

    for (const subtitle of subtitles) {
      const text = type === 'original' ? subtitle.originalText : subtitle.translatedText;
      if (text.trim()) {
        srtContent += `${index}\n`;
        srtContent += `${subtitle.startTime} --> ${subtitle.endTime}\n`;
        srtContent += `${text}\n\n`;
        index++;
      }
    }

    return srtContent;
  }

  async batchProcessVideos(
    videos: { file: File; subtitles: MergedSubtitle[]; options: VideoProcessingOptions }[]
  ): Promise<ProcessedVideoResult[]> {
    const results: ProcessedVideoResult[] = [];

    for (let i = 0; i < videos.length; i++) {
      const { file, subtitles, options } = videos[i];

      try {
        this.updateProgress('processing_video', (i / videos.length) * 100, `Processing video ${i + 1} of ${videos.length}: ${file.name}`);

        const result = await this.processVideoWithSubtitles(file, subtitles, options);
        results.push(result);

      } catch (error) {
        console.error(`Failed to process video ${file.name}:`, error);
        // Continue with next video instead of failing the entire batch
      }
    }

    return results;
  }

  terminate() {
    if (this.ffmpeg) {
      this.ffmpeg.terminate();
      this.isLoaded = false;
    }
  }
}

// Export a singleton instance
export const videoService = new VideoService();
export default videoService;
