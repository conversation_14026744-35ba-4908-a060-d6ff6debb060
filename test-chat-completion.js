// Simple test script to verify the chat completion API endpoint
// Run this with: node test-chat-completion.js

const testChatCompletion = async () => {
  const requestBody = {
    provider: "openai",
    prompt: "Count from 1 to 10",
    stream: false
  };

  try {
    console.log('Testing chat completion API...');
    console.log('Request:', JSON.stringify(requestBody, null, 2));
    
    const response = await fetch('http://localhost:8888/api/chat/completion', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API request failed:', response.status, errorText);
      return;
    }

    const result = await response.json();
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (result.error === 0) {
      console.log('✅ Chat completion API is working correctly!');
      console.log('Provider:', result.data.provider);
      console.log('Model:', result.data.model);
      console.log('Response text:', result.data.response);
    } else {
      console.log('❌ API returned an error:', result.msg);
    }
    
  } catch (error) {
    console.error('❌ Error testing chat completion API:', error.message);
    console.log('Make sure the backend server is running on localhost:8888');
  }
};

// Test with Gemini provider as well
const testGeminiProvider = async () => {
  const requestBody = {
    provider: "gemini",
    prompt: "Say hello in 3 different languages",
    stream: false
  };

  try {
    console.log('\nTesting Gemini provider...');
    console.log('Request:', JSON.stringify(requestBody, null, 2));
    
    const response = await fetch('http://localhost:8888/api/chat/completion', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API request failed:', response.status, errorText);
      return;
    }

    const result = await response.json();
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (result.error === 0) {
      console.log('✅ Gemini provider is working correctly!');
      console.log('Provider:', result.data.provider);
      console.log('Model:', result.data.model);
      console.log('Response text:', result.data.response);
    } else {
      console.log('❌ API returned an error:', result.msg);
    }
    
  } catch (error) {
    console.error('❌ Error testing Gemini provider:', error.message);
  }
};

// Run the tests
const runTests = async () => {
  await testChatCompletion();
  await testGeminiProvider();
};

runTests();
