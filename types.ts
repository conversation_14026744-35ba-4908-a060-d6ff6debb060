
// This interface is used by subtitleUtils for generating SRT content
export interface SubtitleLine {
  id: string;
  startTime: string; // Format: HH:MM:SS,mmm
  endTime: string;   // Format: HH:MM:SS,mmm
  text: string;
}

export interface MergedSubtitle {
  id: string;
  startTime: string;
  endTime: string;
  originalText: string;
  translatedText: string;
}

// Video processing types
export interface VideoProcessingOptions {
  embedOriginalSubtitles: boolean;
  embedTranslatedSubtitles: boolean;
  subtitleStyle: SubtitleStyle;
  outputFormat: 'mp4' | 'webm' | 'avi';
  ttsService?: string;
  ttsLanguageTarget?: 'source' | 'target';
  sourceLanguage?: string;
  targetLanguage?: string;
}

export interface SubtitleStyle {
  fontFamily: string;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'bottom' | 'top' | 'center';
  outline: boolean;
  outlineColor: string;
}

export interface VideoProcessingProgress {
  stage: 'initializing' | 'loading_ffmpeg' | 'processing_video' | 'embedding_subtitles' | 'finalizing' | 'completed' | 'error';
  progress: number; // 0-100
  message: string;
  error?: string;
}

export interface ProcessedVideoResult {
  videoBlob: Blob;
  filename: string;
  duration: number;
  size: number;
}
